<?php

namespace App\Http\Controllers;

use App\Models\QuestionerModel;
use App\Models\DoctorModel;
use App\Models\PatientModel;
use App\Models\PatientAddModel;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Session;
use App\Models\User;


class QuestionController extends Controller
{
    /**
     * Display the questions form.
     *
     * @return \Illuminate\View\View
     */
    public function QuestionS()
    {
        // Get all doctors for the dropdown
        $doctors = DoctorModel::all();

        // Get questions from the questions table dynamically (no caching)
        $questions = \App\Models\Question::orderBy('created_at', 'asc')->get();

        // Add timestamp for debugging
        $timestamp = now()->timestamp;

        // Add cache busting headers
        return response()
            ->view('question_bank', compact('doctors', 'questions', 'timestamp'))
            ->header('Cache-Control', 'no-cache, no-store, must-revalidate')
            ->header('Pragma', 'no-cache')
            ->header('Expires', '0')
            ->header('Last-Modified', gmdate('D, d M Y H:i:s') . ' GMT');
    }
    
    /**
     * Store a patient with questions.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function storePatientWithQuestions(Request $request)
    {
        try {
            // Log the request data for debugging
            \Log::info('Patient form submission:', [
                'all_data' => $request->all(),
                'gender' => $request->gender
            ]);

            // Validate patient data
            $validator = Validator::make($request->all(), [
                'name' => 'required|string|max:255',
                'age' => 'required|numeric',
                'gender' => 'required|string',
                'phone' => 'nullable|string|max:20',
                'email' => 'nullable|email|max:255',
                'province' => 'nullable|string|max:255',
                'district' => 'nullable|string|max:255',
                'village' => 'nullable|string|max:255',
                'Dr_id' => 'required|exists:doctor_models,Dr_Id',
            ]);

            if ($validator->fails()) {
                return redirect()->route('QuestionS')
                    ->withErrors($validator)
                    ->withInput()
                    ->with('error', 'تېروتنه: ' . $validator->errors()->first());
            }

            // Create new patient WITHOUT Total_Score
            $patient = new PatientModel();
            $patient->Patiet_Name = $request->name;
            $patient->Patient_Age = $request->age;
            $patient->Patient_Gender = $request->gender;
            $patient->Patient_phone = $request->phone;
            $patient->Patient_email = $request->email ?? null;
            $patient->Dr_id = $request->Dr_id;
            // Removed Total_Score assignment
            $patient->save();

            // Create patient address if location data is provided
            if ($request->province || $request->district || $request->village) {
                $address = new PatientAddModel();
                $address->Patient_Id = $patient->Patient_id;
                $address->P_Province = $request->province ?? 'نامعلوم';
                $address->P_Distract = $request->district ?? 'نامعلوم';
                $address->P_Village = $request->village ?? 'نامعلوم';
                $address->save();
            }

            // Process questions and answers
            $totalScore = 0; // Still calculate score but don't save it to patient
            $maxPossibleScore = 0;

            // Get all question inputs
            $questionInputs = [];
            foreach ($request->all() as $key => $value) {
                if (strpos($key, 'q') === 0 && is_numeric(substr($key, 1))) {
                    $questionNumber = (int)substr($key, 1);
                    $questionInputs[$questionNumber] = $value;
                }
            }

            // Prepare detailed questions data for notification
            $detailedQuestionsData = [];

            // Get questions from JavaScript array (complete 15 questions)
            $questions = [
                "آیا له دیرشو(۳۰) ورځو راهیسي، تر اوسه دې دا احساس کړی چي ښه یم؟",
                "آیا له دیرشو(۳۰) ورځو راهیسي تر اوسه دې دا احساس کړی چي بدن مي د تقویت درمل ته اړتیا لري؟",
                "آیا له دیرشو(۳۰) ورځو راهیسي، تر اوسه دي د سستي او کمزوري احساس کړی؟",
                "آیا له دیرشو(۳۰) ورځو راهیسي تر اوسه دې د ناروغۍ احساس کړی؟",
                "آیا له دیرشو(۳۰) ورځو راهیسي تر اوسه د سر درد لري؟",
                "آیا له دیرشو(۳۰) ورځو راهیسي تر اوسه دې دا احساس کړی چي سر دي په دستمال وتړم تر څو د درد کم شي؟",
                "آیا له دیرشو(۳۰) ورځو راهیسي تر اوسه دې دا احساس کړی چي بدن مي ګرم یا یخ کیږي؟",
                "آیا له دیرشو(۳۰) ورځو راهیسي د تشویش له امله بې خوبه شوی یې؟",
                "آیا د خوب په منځ کې راویښیږې او خوب دې ګډوډیږي؟",
                "آیا همېشه د فشار لاندې یې؟",
                "آیا د کورنۍ غړو سره ښه اړیکه لرئ؟",
                "آیا د ملګرو سره ښه اړیکه لرئ؟",
                "آیا د کار ځای کې ستونزې لرئ؟",
                "آیا د پیسو ستونزې لرئ؟",
                "آیا د راتلونکي په اړه اندېښنه لرئ؟"
            ];

            // Save each question and answer
            foreach ($questionInputs as $questionNumber => $answer) {
                // Debug logging
                \Log::info('Processing question:', [
                    'questionNumber' => $questionNumber,
                    'answer' => $answer,
                    'questions_array_count' => count($questions),
                    'available_question' => isset($questions[$questionNumber]) ? 'yes' : 'no'
                ]);

                // Get question text from the predefined array
                $questionText = $questions[$questionNumber] ?? "پوښتنه نمبر " . ($questionNumber + 1);

                \Log::info('Question text retrieved:', ['questionText' => $questionText]);

                // Ensure question text is not empty
                if (empty($questionText)) {
                    $questionText = "پوښتنه نمبر " . ($questionNumber + 1);
                }

                // Create question record
                $questionModel = new \App\Models\QuestionerModel();
                $questionModel->Q_Discription = $questionText;
                $questionModel->Question_No = $questionNumber + 1;
                $questionModel->Patient_Id = $patient->Patient_id;

                // Set all answer options
                $questionModel->A = 'هیڅ نه';
                $questionModel->B = 'لږه اندازه';
                $questionModel->C = 'لږه ډېره اندازه';
                $questionModel->D = 'ډېره اندازه';

                // Set the selected option and calculate score
                $questionModel->Selected_Option = match($answer) {
                    'A' => 'هیڅ نه',
                    'B' => 'لږه اندازه',
                    'C' => 'لږه ډېره اندازه',
                    'D' => 'ډېره اندازه',
                    default => $answer
                };

                // Calculate score
                $score = match($answer) {
                    'A' => 0,
                    'B' => 1,
                    'C' => 2,
                    'D' => 3,
                    default => 0
                };
                $totalScore += $score;
                $questionModel->save();
                $maxPossibleScore += 3; // Maximum score per question is 3

                // Add detailed question data for notification
                $detailedQuestionsData[] = [
                    'question_number' => $questionNumber + 1,
                    'question' => $questionText,
                    'option_a' => $optionA,
                    'option_b' => $optionB,
                    'option_c' => $optionC,
                    'option_d' => $optionD,
                    'selected_answer' => $answer,
                    'selected_text' => match($answer) {
                        'A' => $optionA,
                        'B' => $optionB,
                        'C' => $optionC,
                        'D' => $optionD,
                        default => 'نامعلوم'
                    },
                    'score' => match($answer) {
                        'A' => 0,
                        'B' => 1,
                        'C' => 2,
                        'D' => 3,
                        default => 0
                    }
                ];
            }
            
            // Do NOT update patient with total score
            
            // Create notification for questionnaire completion
            \App\Models\Notification::createPatientNotification(
                'questionnaire_completed',
                'نوی ناروغ د پوښتنلیک سره ثبت شو',
                'د ' . $patient->Patiet_Name . ' نوم ناروغ د پوښتنلیک سره ثبت شو. ټول ' . count($questions) . ' پوښتنې ځواب شوي.',
                $patient->Patient_id,
                $patient->Patiet_Name,
                [
                    'age' => $patient->Patient_Age,
                    'gender' => $patient->Patient_Gender,
                    'phone' => $patient->Patient_phone,
                    'email' => $patient->Patient_email,
                    'registration_date' => now()->format('Y-m-d H:i:s')
                ],
                [
                    'total_questions' => count($detailedQuestionsData),
                    'total_score' => $totalScore,
                    'max_score' => $maxPossibleScore,
                    'percentage' => $maxPossibleScore > 0 ? round(($totalScore / $maxPossibleScore) * 100, 2) : 0,
                    'questions_data' => $detailedQuestionsData,
                    'completion_date' => now()->format('Y-m-d H:i:s')
                ]
            );

            // Store notification in session for popup
            session()->flash('notification', [
                'type' => 'success',
                'message' => 'نوی ناروغ په بریالیتوب سره ثبت شو.',
                'patient_name' => $patient->Patiet_Name,
                'patient_id' => $patient->Patient_id
            ]);

            // Return success response with redirect
            return redirect()->route('QuestionS')->with('success', 'د ناروغ معلومات او پوښتنې په بریالیتوب سره ثبت شول');
        } catch (\Exception $e) {
            \Log::error('Error storing patient with questions: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString()
            ]);

            return redirect()->route('QuestionS')->with('error', 'تېروتنه: ' . $e->getMessage());
        }
    }

    /**
     * Show the result for a specific patient.
     *
     * @param  int  $patientId
     * @return \Illuminate\View\View|\Illuminate\Http\RedirectResponse
     */
    public function showResult($patientId)
    {
        try {
            $patient = PatientModel::with('questions')->findOrFail($patientId);
            
            // Calculate scores from questions
            $totalScore = 0;
            $maxPossibleScore = 0;
            
            foreach ($patient->questions as $question) {
                if (!empty($question->A)) $totalScore += 0;
                if (!empty($question->B)) $totalScore += 1;
                if (!empty($question->C)) $totalScore += 2;
                if (!empty($question->D)) $totalScore += 3;
                
                $maxPossibleScore += 3; // Maximum score per question is 3
            }
            
            $percentage = ($maxPossibleScore > 0) ? ($totalScore / $maxPossibleScore) * 100 : 0;
            
            // Determine result category
            $resultCategory = '';
            if ($percentage >= 80) {
                $resultCategory = 'ممتاز';
            } elseif ($percentage >= 60) {
                $resultCategory = 'خوب';
            } elseif ($percentage >= 40) {
                $resultCategory = 'متوسط';
            } else {
                $resultCategory = 'نیاز به ترکیب';
            }
            
            return view('result', compact('patient', 'totalScore', 'maxPossibleScore', 'percentage', 'resultCategory'));
        } catch (\Exception $e) {
            \Log::error('Error showing patient result: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString()
            ]);
            
            return redirect()->back()->with('error', 'تېروتنه: ' . $e->getMessage());
        }
    }

    /**
     * Run the QuestionsSeeder to add default questions.
     */
    public function runQuestionsSeeder()
    {
        try {
            // Run the QuestionsSeeder
            \Artisan::call('db:seed', [
                '--class' => 'Database\\Seeders\\QuestionsSeeder',
                '--force' => true
            ]);
            
            $output = \Artisan::output();
            \Log::info('QuestionsSeeder output: ' . $output);
            
            return redirect()->route('questioner.copy')
                ->with('success', 'د پوښتنو سیډر په بریالیتوب سره اجرا شو!');
        } catch (\Exception $e) {
            \Log::error('Error running QuestionsSeeder: ' . $e->getMessage());
            
            return redirect()->route('questioner.copy')
                ->with('error', 'د پوښتنو سیډر په اجرا کولو کې ستونزه: ' . $e->getMessage());
        }
    }

    /**
     * Delete all questions from the database.
     */
    public function deleteAllQuestions()
    {
        try {
            // Delete all questions from the Question model (not QuestionerModel)
            $count = \App\Models\Question::count();
            \App\Models\Question::truncate();
            
            return redirect()->route('questioner.index')
                ->with('success', $count . ' پوښتنې په بریالیتوب سره لرې شوې!');
        } catch (\Exception $e) {
            \Log::error('Error deleting questions: ' . $e->getMessage());
            
            return redirect()->route('questioner.index')
                ->with('error', 'د پوښتنو په لرې کولو کې ستونزه: ' . $e->getMessage());
        }
    }

    /**
     * Display a listing of the questions.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        // Get regular questions
        $regularQuestions = QuestionerModel::orderBy('Q_Id', 'desc')->get();
        
        // Get seeded questions
        $seededQuestions = \App\Models\Question::orderBy('id', 'desc')->get()->map(function($q) {
            // Convert Question model to format compatible with QuestionerModel
            $question = new \stdClass();
            $question->Q_Id = 'seed_' . $q->id; // Add prefix to distinguish
            $question->Q_Discription = $q->question_text;
            $question->Q_Type = $q->question_type;
            $question->A = $q->option_a ?? 'Option A';
            $question->B = $q->option_b ?? 'Option B';
            $question->C = $q->option_c ?? 'Option C';
            $question->D = $q->option_d ?? 'Option D';
            $question->is_seeded = true; // Flag to identify seeded questions
            return $question;
        });
        
        // Combine both collections
        $allQuestions = $regularQuestions->concat($seededQuestions);
        
        // Paginate the results
        $questions = new \Illuminate\Pagination\LengthAwarePaginator(
            $allQuestions->forPage(\Illuminate\Pagination\Paginator::resolveCurrentPage(), 10),
            $allQuestions->count(),
            10,
            null,
            ['path' => \Illuminate\Pagination\Paginator::resolveCurrentPath()]
        );
        
        return view('dashboardofproject.questionercopy', compact('questions'));
    }

    /**
     * Store a newly created question in storage.
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'question_text' => 'required|string|max:500',
            'question_type' => 'required|string|in:mental,physical,social',
            'option_a' => 'required|string|max:255',
            'option_b' => 'required|string|max:255',
            'option_c' => 'required|string|max:255',
            'option_d' => 'required|string|max:255',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        QuestionerModel::create([
            'Q_Discription' => $request->question_text,
            'Q_Type' => $request->question_type,
            'A' => $request->option_a,
            'B' => $request->option_b,
            'C' => $request->option_c,
            'D' => $request->option_d,
        ]);

        return redirect()->route('questioner.index')
            ->with('success', 'پوښتنه په بریالیتوب سره اضافه شوه!');
    }

    /**
     * Show the form for editing the specified question.
     *
     * @param  int  $id
     * @return \Illuminate\View\View
     */
    public function edit($id)
    {
        // Find the question to edit
        $editQuestion = QuestionerModel::findOrFail($id);
        
        // Get all questions for the table
        $regularQuestions = QuestionerModel::orderBy('Q_Id', 'desc')->get();
        
        // Get seeded questions
        $seededQuestions = \App\Models\Question::orderBy('id', 'desc')->get()->map(function($q) {
            // Convert Question model to format compatible with QuestionerModel
            $question = new \stdClass();
            $question->Q_Id = 'seed_' . $q->id; // Add prefix to distinguish
            $question->Q_Discription = $q->question_text;
            $question->Q_Type = $q->question_type;
            $question->A = $q->option_a ?? 'Option A';
            $question->B = $q->option_b ?? 'Option B';
            $question->C = $q->option_c ?? 'Option C';
            $question->D = $q->option_d ?? 'Option D';
            $question->is_seeded = true; // Flag to identify seeded questions
            return $question;
        });
        
        // Combine both collections
        $allQuestions = $regularQuestions->concat($seededQuestions);
        
        // Paginate the results
        $questions = new \Illuminate\Pagination\LengthAwarePaginator(
            $allQuestions->forPage(\Illuminate\Pagination\Paginator::resolveCurrentPage(), 10),
            $allQuestions->count(),
            10,
            null,
            ['path' => \Illuminate\Pagination\Paginator::resolveCurrentPath()]
        );
        
        return view('dashboardofproject.questionercopy', compact('editQuestion', 'questions'));
    }

    /**
     * Update the specified question in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(Request $request, $id)
    {
        try {
            // Validate request
            $request->validate([
                'question_text' => 'required|string',
                'question_type' => 'required|string|in:mental,physical,social',
                'option_a' => 'required|string',
                'option_b' => 'required|string',
                'option_c' => 'required|string',
                'option_d' => 'required|string',
            ]);

            // Find the question
            $question = QuestionerModel::findOrFail($id);
            
            // Update question
            $question->Q_Discription = $request->question_text;
            $question->Q_Type = $request->question_type;
            $question->A = $request->option_a;
            $question->B = $request->option_b;
            $question->C = $request->option_c;
            $question->D = $request->option_d;
            
            $question->save();
            
            return redirect()->route('questioner.index')->with('success', 'پوښتنه په بریالیتوب سره تازه شوه!');
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'د پوښتنې په تازه کولو کې ستونزه: ' . $e->getMessage());
        }
    }

    /**
     * Remove the specified question from storage.
     *
     * @param  string|int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroy($id)
    {
        try {
            // Check if this is a seeded question (has prefix)
            if (is_string($id) && strpos($id, 'seed_') === 0) {
                $realId = substr($id, 5); // Remove 'seed_' prefix
                $question = \App\Models\Question::findOrFail($realId);
            } else {
                $question = QuestionerModel::findOrFail($id);
            }
            
            $question->delete();
            
            return redirect()->route('questioner.index')
                ->with('success', 'پوښتنه په بریالیتوب سره لرې شوه!');
        } catch (\Exception $e) {
            \Log::error('Error deleting question: ' . $e->getMessage());
            
            return redirect()->route('questioner.index')
                ->with('error', 'د پوښتنې په لرې کولو کې ستونزه: ' . $e->getMessage());
        }
    }
}































































