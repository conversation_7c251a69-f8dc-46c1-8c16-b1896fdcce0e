
// Function to get current questions (prioritize database questions)
function getCurrentQuestions() {
    if (window.databaseQuestions && window.databaseQuestions.length > 0) {
        return window.databaseQuestions;
    }
    return [
        window.trans_static ? window.trans_static("آیا له دیرشو(۳۰) ورځو راهیسي، تر اوسه دې دا احساس کړی چي ښه یم؟") : "آیا له دیرشو(۳۰) ورځو راهیسي، تر اوسه دې دا احساس کړی چي ښه یم؟",
        window.trans_static ? window.trans_static("آیا له دیرشو(۳۰) ورځو راهیسي تر اوسه دې دا احساس کړی چي بدن مي د تقویت درمل ته اړتیا لري؟") : "آیا له دیرشو(۳۰) ورځو راهیسي تر اوسه دې دا احساس کړی چي بدن مي د تقویت درمل ته اړتیا لري؟",
        window.trans_static ? window.trans_static("آیا له دیرشو(۳۰) ورځو راهیسي، تر اوسه دي د سستي او کمزوري احساس کړی؟") : "آیا له دیرشو(۳۰) ورځو راهیسي، تر اوسه دي د سستي او کمزوري احساس کړی؟"
    ];
}

// Function to create the question form
function createQuestionForm() {
    const form = document.getElementById("mentalForm");
    if (!form) return;

    // Get current questions (database or fallback)
    const questions = getCurrentQuestions();

    // Clear any existing question steps (keep step-0 which is personal info)
    const existingSteps = form.querySelectorAll('.form-step:not(#step-0)');
    existingSteps.forEach(step => step.remove());



    questions.forEach((question, index) => {
const stepDiv = document.createElement("div");
stepDiv.className = "form-step";
stepDiv.id = `step-${index + 1}`;

const q = document.createElement("p");
q.textContent = `${index + 1} - ${question}`;
stepDiv.appendChild(q);

// Use dynamic options if available, otherwise use default
const options = (window.questionOptions && window.questionOptions[index]) || [
  { value: "A", text: window.trans_static ? window.trans_static("هیڅ نه") : "هیڅ نه" },
  { value: "B", text: window.trans_static ? window.trans_static("لږه اندازه") : "لږه اندازه" },
  { value: "C", text: window.trans_static ? window.trans_static("لږه ډېره اندازه") : "لږه ډېره اندازه" },
  { value: "D", text: window.trans_static ? window.trans_static("ډېره اندازه") : "ډېره اندازه" }
];
options.forEach(opt => {
const label = document.createElement("label");
label.className = "radio-group";
label.innerHTML = `<input type="radio" name="q${index}" value="${opt.value}"> ${opt.text}`;
stepDiv.appendChild(label);
});

const button = document.createElement("button");
if (index < questions.length - 1) {
button.type = "button";
button.textContent = window.trans_static ? window.trans_static("بله پوښتنه") : "بله پوښتنه";
button.onclick = () => window.nextStep();
} else {
button.type = "submit";
button.textContent = window.trans_static ? window.trans_static("لیږل") : "لیږل";
}

stepDiv.appendChild(button);
form.appendChild(stepDiv);
    });
}

// Make function globally available
window.createQuestionForm = createQuestionForm;

// Call the function to create the form
// Wait for database questions to be loaded
setTimeout(function() {
    createQuestionForm();
}, 200);

// nextStep function is now defined in the template

    form.addEventListener("submit", function(e) {
      // Allow the form to submit normally to the server
      // The server will handle the data storage and redirect
    });

    // showError and clearErrors functions are now defined in the template
}