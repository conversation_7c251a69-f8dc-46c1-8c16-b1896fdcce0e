
// Function to get current questions (prioritize database questions)
function getCurrentQuestions() {
    if (window.databaseQuestions && window.databaseQuestions.length > 0) {
        return window.databaseQuestions;
    }
    return [
        window.trans_static ? window.trans_static("آیا له دیرشو(۳۰) ورځو راهیسي، تر اوسه دې دا احساس کړی چي ښه یم؟") : "آیا له دیرشو(۳۰) ورځو راهیسي، تر اوسه دې دا احساس کړی چي ښه یم؟",
        window.trans_static ? window.trans_static("آیا له دیرشو(۳۰) ورځو راهیسي تر اوسه دې دا احساس کړی چي بدن مي د تقویت درمل ته اړتیا لري؟") : "آیا له دیرشو(۳۰) ورځو راهیسي تر اوسه دې دا احساس کړی چي بدن مي د تقویت درمل ته اړتیا لري؟",
        window.trans_static ? window.trans_static("آیا له دیرشو(۳۰) ورځو راهیسي، تر اوسه دي د سستي او کمزوري احساس کړی؟") : "آیا له دیرشو(۳۰) ورځو راهیسي، تر اوسه دي د سستي او کمزوري احساس کړی؟"
    ];
}

// Function to create the question form
function createQuestionForm() {
    const form = document.getElementById("mentalForm");
    if (!form) return;

    // Get current questions (database or fallback)
    const questions = getCurrentQuestions();

    // Clear any existing question steps (keep step-0 which is personal info)
    const existingSteps = form.querySelectorAll('.form-step:not(#step-0)');
    existingSteps.forEach(step => step.remove());



    questions.forEach((question, index) => {
const stepDiv = document.createElement("div");
stepDiv.className = "form-step";
stepDiv.id = `step-${index + 1}`;

const q = document.createElement("p");
q.textContent = `${index + 1} - ${question}`;
stepDiv.appendChild(q);

// Use dynamic options if available, otherwise use default
const options = (window.questionOptions && window.questionOptions[index]) || [
  { value: "A", text: window.trans_static ? window.trans_static("هیڅ نه") : "هیڅ نه" },
  { value: "B", text: window.trans_static ? window.trans_static("لږه اندازه") : "لږه اندازه" },
  { value: "C", text: window.trans_static ? window.trans_static("لږه ډېره اندازه") : "لږه ډېره اندازه" },
  { value: "D", text: window.trans_static ? window.trans_static("ډېره اندازه") : "ډېره اندازه" }
];
options.forEach(opt => {
const label = document.createElement("label");
label.className = "radio-group";
label.innerHTML = `<input type="radio" name="q${index}" value="${opt.value}"> ${opt.text}`;
stepDiv.appendChild(label);
});

const button = document.createElement("button");
if (index < questions.length - 1) {
button.type = "button";
button.textContent = window.trans_static ? window.trans_static("بله پوښتنه") : "بله پوښتنه";
button.onclick = () => nextStep();
} else {
button.type = "submit";
button.textContent = window.trans_static ? window.trans_static("لیږل") : "لیږل";
}

stepDiv.appendChild(button);
form.appendChild(stepDiv);
    });
}

// Make function globally available
window.createQuestionForm = createQuestionForm;

// Call the function to create the form
// Wait for database questions to be loaded
setTimeout(function() {
    createQuestionForm();
}, 200);

let currentStep = 0;

    function nextStep() {
  clearErrors();
  const currentDiv = document.getElementById(`step-${currentStep}`);
  const inputs = currentDiv.querySelectorAll("input[type='text']");
  const radios = currentDiv.querySelectorAll("input[type='radio']");
  let valid = true;

  if (currentStep === 0) {
    inputs.forEach(input => {
      const name = input.name;
      const value = input.value.trim();

      if (name === "name") {
        if (!/^[\u0600-\u06FF\s]{1,25}$/.test(value)) {
          showError(input, "نوم باید تر ۲۵ حروفو کم وي او هیڅ عدد یا سمبول پکې نه وي");
          valid = false;
        }
      }

      if (name === "phone") {
        if (!/^07\d{8}$/.test(value)) {
          showError(input, "تلیفون باید له ۱۰ عدده جوړ وي او له 07 پیل شي");
          valid = false;
        }
      }

      if (name === "age") {
        const age = parseInt(value);
        if (isNaN(age) || age <= 0 || age > 60) {
          showError(input, "عمر باید عدد وي او تر ۶۰ کم وي");
          valid = false;
        }
      }

      if (["province", "district", "village"].includes(name)) {
        if (!/^[\u0600-\u06FF\s]{1,10}$/.test(value)) {
          showError(input, "یواځې حروف وکاروئ (تر ۱۰ کم)، عدد یا سمبول مه داخلوئ");
          valid = false;
        }
      }
    });

    const genderContainer = currentDiv.querySelector('label[for="gender"]') || currentDiv;
    const genderInputs = currentDiv.querySelectorAll('input[name="gender"]');
    const genderChecked = Array.from(genderInputs).some(r => r.checked);

    if (!genderChecked) {
      const lastRadio = genderInputs[genderInputs.length - 1];
      showError(lastRadio, "مهرباني وکړئ خپل جنسیت وټاکئ");
      valid = false;
    }

  } else {
    // د پوښتنو مرحله
    valid = Array.from(radios).some(r => r.checked);
    if (!valid) {
      const q = currentDiv.querySelector("p");
      const error = document.createElement("div");
      error.classList.add("error-msg");
      error.textContent = "مهرباني وکړئ دا پوښتنه ځواب کړئ";
      error.style.color = "red";
      currentDiv.appendChild(error);
    }
  }

  if (!valid) return;

  currentDiv.classList.remove("active");
  currentStep++;
  const nextDiv = document.getElementById(`step-${currentStep}`);
  if (nextDiv) nextDiv.classList.add("active");
    }

    form.addEventListener("submit", function(e) {
      // Allow the form to submit normally to the server
      // The server will handle the data storage and redirect
      console.log("Form is being submitted to server...");
    });

    function showError(input, message) {
        let error = input.nextElementSibling;
        if (!error || !error.classList.contains('error-msg')) {
            error = document.createElement('div');
            error.classList.add('error-msg');
            error.style.color = 'red';
            error.style.fontSize = '14px';
            input.insertAdjacentElement('afterend', error);
        }
        error.textContent = message;
    }

    function clearErrors() {
        document.querySelectorAll('.error-msg').forEach(e => e.remove());
    }
}