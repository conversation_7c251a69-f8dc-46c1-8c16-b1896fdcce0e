<?php $__env->startSection('title', trans_static('د رواني روغتیا پوښتنلیک')); ?>

<?php $__env->startSection('contents'); ?>

<!-- Simple Beautiful Design -->
<div id="fgfgfgfg">
  
  <!-- Beautiful Form Card -->
  <div class="beautiful-form-card">
    
    <!-- Header -->
    <div class="form-header">
      <div class="header-icon">
        <i class="fas fa-user-circle"></i>
      </div>
      <h2><?php echo e(trans_static('شخصي معلومات')); ?></h2>
      <p><?php echo e(trans_static('مهرباني وکړئ خپل معلومات ولیکئ')); ?></p>

      <!-- Refresh Questions Button -->
      <!-- <button onclick="refreshQuestions()" style="position: absolute; top: 15px; right: 15px; background: #007bff; color: white; border: none; padding: 8px 12px; border-radius: 6px; cursor: pointer; font-size: 12px;">
        <i class="fas fa-sync-alt"></i> <?php echo e(trans_static('د پوښتنو تازه کول')); ?>

      </button> -->
    </div>

    <!-- Success/Error Messages -->
    <?php if(session('success')): ?>
      <div class="alert alert-success" style="background-color: #d4edda; color: #155724; padding: 15px; border-radius: 8px; margin-bottom: 20px; border: 1px solid #c3e6cb;">
        <i class="fas fa-check-circle"></i> <?php echo e(session('success')); ?>

      </div>
    <?php endif; ?>

    <?php if(session('error')): ?>
      <div class="alert alert-error" style="background-color: #f8d7da; color: #721c24; padding: 15px; border-radius: 8px; margin-bottom: 20px; border: 1px solid #f5c6cb;">
        <i class="fas fa-exclamation-circle"></i> <?php echo e(session('error')); ?>

      </div>
    <?php endif; ?>

    <!-- Simple Beautiful Form -->
    <form id="mentalForm" method="POST" action="<?php echo e(route('storePatientWithQuestions')); ?>">
      <?php echo csrf_field(); ?>
      
      <div class="form-step active" id="step-0">
        
        <!-- Name Input -->
        <label>
          <i class="fas fa-user"></i>
          <?php echo e(trans_static('ستاسو نوم')); ?> *
          <input type="text" name="name" required placeholder="<?php echo e(trans_static('خپل بشپړ نوم ولیکئ')); ?>">
        </label>

        <!-- Age Input -->
        <label>
          <i class="fas fa-calendar"></i>
          <?php echo e(trans_static('ستاسو عمر')); ?> *
          <input type="number" name="age" required min="1" max="120" placeholder="<?php echo e(trans_static('خپل عمر ولیکئ')); ?>">
        </label>

        <!-- Phone Input -->
        <label>
          <i class="fas fa-phone"></i>
          <?php echo e(trans_static('ستاسو د اړیکې شمیره')); ?>

          <input type="tel" name="phone" placeholder="**********">
        </label>

        <!-- Email Input -->
        <label>
          <i class="fas fa-envelope"></i>
          <?php echo e(trans_static('ستاسو ایمیل')); ?>

          <input type="email" name="email" placeholder="<EMAIL>">
        </label>

        <!-- Gender Selection -->
        <label class="gender-label">
          <i class="fas fa-venus-mars"></i>
          <?php echo e(trans_static('ستاسو جنسیت')); ?> *
        </label>
        <div class="gender-options">
          <label class="gender-option">
            <input type="radio" name="gender" value="<?php echo e(trans_static('نارینه')); ?>" required>
            <div class="gender-card male">
              <i class="fas fa-mars"></i>
              <span><?php echo e(trans_static('نارینه')); ?></span>
            </div>
          </label>
          <label class="gender-option">
            <input type="radio" name="gender" value="<?php echo e(trans_static('ښځینه')); ?>" required>
            <div class="gender-card female">
              <i class="fas fa-venus"></i>
              <span><?php echo e(trans_static('ښځینه')); ?></span>
            </div>
          </label>
        </div>

        <!-- Location Inputs -->
        <div class="location-group">
          <label>
            <i class="fas fa-map"></i>
            <?php echo e(trans_static('ولایت')); ?>

            <input type="text" name="province" placeholder="<?php echo e(trans_static('کندهار')); ?>">
          </label>
          <label>
            <i class="fas fa-map-marker"></i>
            <?php echo e(trans_static('ولسوالي')); ?>

            <input type="text" name="district" placeholder="<?php echo e(trans_static('ښار')); ?>">
          </label>
          <label>
            <i class="fas fa-home"></i>
            <?php echo e(trans_static('کلی')); ?>

            <input type="text" name="village" placeholder="<?php echo e(trans_static('مرکز')); ?>">
          </label>
        </div>

        <!-- Doctor Selection -->
        <label>
          <i class="fas fa-user-md"></i>
          <?php echo e(trans_static('ډاکټر انتخاب کړئ')); ?> *
          <select name="Dr_id" required>
            <option value=""><?php echo e(trans_static('ډاکټر انتخاب کړئ')); ?></option>
            <?php $__currentLoopData = $doctors; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $doctor): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
              <option value="<?php echo e($doctor->Dr_Id); ?>"><?php echo e($doctor->Dr_Name); ?></option>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
          </select>
        </label>

        <!-- Submit Button -->
        <button type="button" onclick="nextStep()" class="beautiful-button">
          <i class="fas fa-arrow-left"></i>
          د پوښتنو پیل
        </button>

      </div>
    </form>
  </div>
</div>

<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<script>
// Global variables and functions
window.currentStep = 0;
window.databaseQuestions = [];
window.questionOptions = [];

// Pass trans_static function to JavaScript
window.trans_static = function(key) {
    return key; // Simple passthrough for now
};

// Pass questions from database to JavaScript
<?php if($questions && $questions->count() > 0): ?>
    <?php $__currentLoopData = $questions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $question): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        window.databaseQuestions.push(<?php echo json_encode($question->question_text); ?>);
        window.questionOptions.push([
            {value: 'A', text: <?php echo json_encode($question->A); ?>},
            {value: 'B', text: <?php echo json_encode($question->B); ?>},
            {value: 'C', text: <?php echo json_encode($question->C); ?>},
            {value: 'D', text: <?php echo json_encode($question->D); ?>}
        ]);
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
<?php endif; ?>

// Define nextStep function immediately
window.nextStep = function() {
    window.clearErrors();
    const currentDiv = document.getElementById('step-' + window.currentStep);
    if (!currentDiv) return;

    const inputs = currentDiv.querySelectorAll("input[type='text']");
    const radios = currentDiv.querySelectorAll("input[type='radio']");
    let valid = true;

    if (window.currentStep === 0) {
        inputs.forEach(function(input) {
            const name = input.name;
            const value = input.value.trim();

            if (name === "name") {
                if (!/^[\u0600-\u06FF\s]{1,25}$/.test(value)) {
                    window.showError(input, "نوم باید تر ۲۵ حروفو کم وي او هیڅ عدد یا سمبول پکې نه وي");
                    valid = false;
                }
            }

            if (name === "phone") {
                if (!/^07\d{8}$/.test(value)) {
                    window.showError(input, "تلیفون باید له ۱۰ عدده جوړ وي او له 07 پیل شي");
                    valid = false;
                }
            }

            if (name === "age") {
                const age = parseInt(value);
                if (isNaN(age) || age <= 0 || age > 60) {
                    window.showError(input, "عمر باید عدد وي او تر ۶۰ کم وي");
                    valid = false;
                }
            }

            if (["province", "district", "village"].includes(name)) {
                if (!/^[\u0600-\u06FF\s]{1,10}$/.test(value)) {
                    window.showError(input, "یواځې حروف وکاروئ (تر ۱۰ کم)، عدد یا سمبول مه داخلوئ");
                    valid = false;
                }
            }
        });

        const genderInputs = currentDiv.querySelectorAll('input[name="gender"]');
        const genderChecked = Array.from(genderInputs).some(function(r) { return r.checked; });

        if (!genderChecked) {
            const lastRadio = genderInputs[genderInputs.length - 1];
            window.showError(lastRadio, "مهرباني وکړئ خپل جنسیت وټاکئ");
            valid = false;
        }

    } else {
        // د پوښتنو مرحله
        valid = Array.from(radios).some(function(r) { return r.checked; });
        if (!valid) {
            const error = document.createElement("div");
            error.classList.add("error-msg");
            error.textContent = "مهرباني وکړئ دا پوښتنه ځواب کړئ";
            error.style.color = "red";
            currentDiv.appendChild(error);
        }
    }

    if (!valid) return;

    currentDiv.classList.remove("active");
    window.currentStep++;
    const nextDiv = document.getElementById('step-' + window.currentStep);
    if (nextDiv) nextDiv.classList.add("active");
};

// Define helper functions
window.showError = function(input, message) {
    let error = input.nextElementSibling;
    if (!error || !error.classList.contains('error-msg')) {
        error = document.createElement('div');
        error.classList.add('error-msg');
        error.style.color = 'red';
        error.style.fontSize = '14px';
        input.insertAdjacentElement('afterend', error);
    }
    error.textContent = message;
};

window.clearErrors = function() {
    document.querySelectorAll('.error-msg').forEach(function(e) { e.remove(); });
};

// Function to refresh questions from server
function refreshQuestions() {
    const refreshBtn = document.querySelector('button[onclick="refreshQuestions()"]');
    if (refreshBtn) {
        refreshBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> <?php echo e(trans_static("په لوډولو کې...")); ?>';
        refreshBtn.disabled = true;
    }

    fetch('/questions-for-questionnaire')
        .then(response => response.json())
        .then(data => {
            if (data.success && data.questions) {
                // Update questions arrays
                window.databaseQuestions = data.questions.map(q => q.question_text);
                window.questionOptions = data.questions.map(q => [
                    {value: 'A', text: q.A},
                    {value: 'B', text: q.B},
                    {value: 'C', text: q.C},
                    {value: 'D', text: q.D}
                ]);

                // Regenerate the form with new questions
                if (typeof window.createQuestionForm === 'function') {
                    window.createQuestionForm();
                } else {
                    regenerateQuestionForm();
                }

                // Show success feedback
                if (refreshBtn) {
                    refreshBtn.innerHTML = '<i class="fas fa-check"></i> <?php echo e(trans_static("تازه شول")); ?>';
                    setTimeout(() => {
                        refreshBtn.innerHTML = '<i class="fas fa-sync-alt"></i> <?php echo e(trans_static("د پوښتنو تازه کول")); ?>';
                        refreshBtn.disabled = false;
                    }, 2000);
                }
            }
        })
        .catch(error => {
            console.error('Error refreshing questions:', error);

            // Show error feedback
            if (refreshBtn) {
                refreshBtn.innerHTML = '<i class="fas fa-times"></i> <?php echo e(trans_static("ستونزه")); ?>';
                setTimeout(() => {
                    refreshBtn.innerHTML = '<i class="fas fa-sync-alt"></i> <?php echo e(trans_static("د پوښتنو تازه کول")); ?>';
                    refreshBtn.disabled = false;
                }, 2000);
            }
        });
}

// Function to regenerate the question form with updated questions
function regenerateQuestionForm() {
    const form = document.getElementById("mentalForm");
    if (!form) return;

    // Remove all existing question steps (keep step-0 which is personal info)
    const existingSteps = form.querySelectorAll('.form-step:not(#step-0)');
    existingSteps.forEach(step => step.remove());

    // Check if we have questions to work with
    if (!window.databaseQuestions || window.databaseQuestions.length === 0) {
        console.warn('No questions available to regenerate form');
        return;
    }

    // Regenerate question steps
    window.databaseQuestions.forEach((question, index) => {
        const stepDiv = document.createElement("div");
        stepDiv.className = "form-step";
        stepDiv.id = `step-${index + 1}`;

        const q = document.createElement("p");
        q.textContent = `${index + 1} - ${question}`;
        stepDiv.appendChild(q);

        // Use dynamic options with proper structure
        const options = (window.questionOptions && window.questionOptions[index]) || [
            { value: "A", text: "هیڅ نه" },
            { value: "B", text: "لږه اندازه" },
            { value: "C", text: "لږه ډېره اندازه" },
            { value: "D", text: "ډېره اندازه" }
        ];

        options.forEach(opt => {
            const label = document.createElement("label");
            label.className = "radio-group";
            label.innerHTML = `<input type="radio" name="q${index}" value="${opt.value}"> ${opt.text}`;
            stepDiv.appendChild(label);
        });

        const button = document.createElement("button");
        if (index < window.databaseQuestions.length - 1) {
            button.type = "button";
            button.textContent = "بله پوښتنه";
            button.onclick = function() { window.nextStep(); };
        } else {
            button.type = "submit";
            button.textContent = "لیږل";
        }
        button.className = "beautiful-button";
        stepDiv.appendChild(button);

        form.appendChild(stepDiv);
    });
}

// Functions already defined above

// Auto-refresh questions every 30 seconds
setInterval(refreshQuestions, 30000);

// Refresh questions when page becomes visible (user switches back to tab)
document.addEventListener('visibilitychange', function() {
    if (!document.hidden) {
        refreshQuestions();
    }
});

// Initialize the form with database questions on page load
document.addEventListener('DOMContentLoaded', function() {
    // Wait a bit for Questions.js to load, then regenerate with database questions
    setTimeout(function() {
        if (window.databaseQuestions && window.databaseQuestions.length > 0) {
            if (typeof window.createQuestionForm === 'function') {
                window.createQuestionForm();
            } else {
                regenerateQuestionForm();
            }
        }
    }, 500);
});
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts/Main', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\KU_Mental_Health\resources\views/question_bank.blade.php ENDPATH**/ ?>